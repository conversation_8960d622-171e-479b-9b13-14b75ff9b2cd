<script setup lang="ts">
import { RouterView } from 'vue-router'
import Toast from 'primevue/toast'
import ConfirmDialog from 'primevue/confirmdialog'
import { useToast } from 'primevue'
import { onMounted } from 'vue'
import { API_ERROR_EVENT } from './variables'
import type { AxiosResponse } from 'axios'

const toast = useToast()
onMounted(() => {
  window.addEventListener(API_ERROR_EVENT, (event: Event) => {
    const { detail } = event as CustomEvent<AxiosResponse>

    if (detail.status >= 500) {
      toast.add({
        severity: 'warn',
        summary: '服务器抽筋了！',
        detail: '服务器遇到了一些问题，请稍后再试。',
      })
    } else {
      toast.add({
        severity: 'error',
        summary: `${detail.status} ${detail.statusText}`,
        detail: detail.data?.message,
      })
    }
  })
})
</script>

<template>
  <RouterView />
  <Toast />
  <ConfirmDialog />
</template>

<style scoped></style>
